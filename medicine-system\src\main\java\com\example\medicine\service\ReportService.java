package com.example.medicine.service;

import java.util.List;
import java.util.Map;

public interface ReportService {
    
    /**
     * 获取报表概览数据
     * @param startDate 开始日期 (yyyy-MM-dd)
     * @param endDate 结束日期 (yyyy-MM-dd)
     * @return 概览数据
     */
    Map<String, Object> getOverviewData(String startDate, String endDate);
    
    /**
     * 获取销售趋势数据
     * @param type 类型：daily/monthly
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售趋势数据
     */
    Map<String, Object> getSalesTrendData(String type, String startDate, String endDate);
    
    /**
     * 获取药品销售排行
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 药品排行数据
     */
    List<Map<String, Object>> getMedicineRankData(String startDate, String endDate, Integer limit);
    
    /**
     * 获取客户分布数据
     * @return 客户分布数据
     */
    List<Map<String, Object>> getCustomerDistributionData();
    
    /**
     * 获取供应商占比数据
     * @return 供应商占比数据
     */
    List<Map<String, Object>> getSupplierRatioData();
    
    /**
     * 获取库存状态数据
     * @return 库存状态数据
     */
    List<Map<String, Object>> getInventoryStatusData();
    
    /**
     * 获取热销药品排行
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param limit 限制数量
     * @return 热销药品数据
     */
    List<Map<String, Object>> getHotMedicinesData(String startDate, String endDate, Integer limit);
    
    /**
     * 获取库存预警数据
     * @return 库存预警数据
     */
    List<Map<String, Object>> getLowStockData();
}
