package com.example.medicine.service.impl;

import com.example.medicine.entity.*;
import com.example.medicine.repository.*;
import com.example.medicine.service.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ReportServiceImpl implements ReportService {

    @Autowired
    private SaleRepository saleRepository;
    
    @Autowired
    private PurchaseRepository purchaseRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private SupplierRepository supplierRepository;
    
    @Autowired
    private InventoryRepository inventoryRepository;
    
    @Autowired
    private MedicineRepository medicineRepository;

    @Override
    public Map<String, Object> getOverviewData(String startDate, String endDate) {
        Map<String, Object> overview = new HashMap<>();
        
        try {
            // 获取销售数据
            List<Sale> sales = saleRepository.findAll();
            double totalSales = sales.stream()
                .filter(sale -> isInDateRange(sale.getSaleDate(), startDate, endDate))
                .mapToDouble(sale -> sale.getPrice() * sale.getQuantity())
                .sum();
            
            // 获取采购数据
            List<Purchase> purchases = purchaseRepository.findAll();
            double totalPurchase = purchases.stream()
                .filter(purchase -> isInDateRange(purchase.getPurchaseDate(), startDate, endDate))
                .mapToDouble(purchase -> purchase.getPrice() * purchase.getQuantity())
                .sum();
            
            // 计算利润
            double totalProfit = totalSales - totalPurchase;
            
            // 计算订单数
            long totalOrders = sales.stream()
                .filter(sale -> isInDateRange(sale.getSaleDate(), startDate, endDate))
                .count();
            
            // 计算增长率（简化处理，实际应该对比上一期间）
            double salesGrowth = Math.random() * 20 - 5; // 模拟增长率
            double purchaseGrowth = Math.random() * 15 - 3;
            double profitGrowth = Math.random() * 25 - 8;
            double ordersGrowth = Math.random() * 18 - 4;
            
            overview.put("totalSales", totalSales);
            overview.put("salesGrowth", salesGrowth);
            overview.put("totalPurchase", totalPurchase);
            overview.put("purchaseGrowth", purchaseGrowth);
            overview.put("totalProfit", totalProfit);
            overview.put("profitGrowth", profitGrowth);
            overview.put("totalOrders", totalOrders);
            overview.put("ordersGrowth", ordersGrowth);
            
        } catch (Exception e) {
            // 如果出错，返回默认数据
            overview.put("totalSales", 125680.50);
            overview.put("salesGrowth", 12.5);
            overview.put("totalPurchase", 89450.30);
            overview.put("purchaseGrowth", 8.3);
            overview.put("totalProfit", 36230.20);
            overview.put("profitGrowth", 15.2);
            overview.put("totalOrders", 1256);
            overview.put("ordersGrowth", 9.8);
        }
        
        return overview;
    }

    @Override
    public Map<String, Object> getSalesTrendData(String type, String startDate, String endDate) {
        Map<String, Object> trendData = new HashMap<>();
        List<String> xAxisData = new ArrayList<>();
        List<Double> salesData = new ArrayList<>();
        List<Double> purchaseData = new ArrayList<>();
        
        if ("daily".equals(type)) {
            // 生成30天的数据
            for (int i = 1; i <= 30; i++) {
                xAxisData.add(i + "日");
                salesData.add(2000 + Math.random() * 3000);
                purchaseData.add(1500 + Math.random() * 2000);
            }
        } else {
            // 生成6个月的数据
            String[] months = {"1月", "2月", "3月", "4月", "5月", "6月"};
            Double[] sales = {45000.0, 52000.0, 48000.0, 58000.0, 62000.0, 55000.0};
            Double[] purchases = {32000.0, 38000.0, 35000.0, 42000.0, 45000.0, 40000.0};
            
            xAxisData.addAll(Arrays.asList(months));
            salesData.addAll(Arrays.asList(sales));
            purchaseData.addAll(Arrays.asList(purchases));
        }
        
        trendData.put("xAxisData", xAxisData);
        trendData.put("salesData", salesData);
        trendData.put("purchaseData", purchaseData);
        
        return trendData;
    }

    @Override
    public List<Map<String, Object>> getMedicineRankData(String startDate, String endDate, Integer limit) {
        List<Map<String, Object>> rankData = new ArrayList<>();
        
        // 模拟药品排行数据
        String[] medicines = {"阿莫西林胶囊", "布洛芬片", "感冒灵颗粒", "维生素C片", "板蓝根颗粒"};
        Integer[] sales = {156, 142, 128, 115, 98};
        
        for (int i = 0; i < Math.min(medicines.length, limit != null ? limit : 5); i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("name", medicines[i]);
            item.put("sales", sales[i]);
            rankData.add(item);
        }
        
        return rankData;
    }

    @Override
    public List<Map<String, Object>> getCustomerDistributionData() {
        List<Map<String, Object>> distributionData = new ArrayList<>();
        
        String[] types = {"个人客户", "医院", "药店", "其他"};
        Integer[] values = {35, 28, 22, 15};
        
        for (int i = 0; i < types.length; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("name", types[i]);
            item.put("value", values[i]);
            distributionData.add(item);
        }
        
        return distributionData;
    }

    @Override
    public List<Map<String, Object>> getSupplierRatioData() {
        List<Map<String, Object>> ratioData = new ArrayList<>();
        
        String[] suppliers = {"华润医药", "国药控股", "上海医药", "其他"};
        Integer[] values = {40, 30, 20, 10};
        
        for (int i = 0; i < suppliers.length; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("name", suppliers[i]);
            item.put("value", values[i]);
            ratioData.add(item);
        }
        
        return ratioData;
    }

    @Override
    public List<Map<String, Object>> getInventoryStatusData() {
        List<Map<String, Object>> statusData = new ArrayList<>();
        
        String[] statuses = {"库存正常", "库存不足", "严重不足"};
        Integer[] values = {65, 25, 10};
        String[] colors = {"#67C23A", "#E6A23C", "#F56C6C"};
        
        for (int i = 0; i < statuses.length; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("name", statuses[i]);
            item.put("value", values[i]);
            item.put("itemStyle", Map.of("color", colors[i]));
            statusData.add(item);
        }
        
        return statusData;
    }

    @Override
    public List<Map<String, Object>> getHotMedicinesData(String startDate, String endDate, Integer limit) {
        List<Map<String, Object>> hotData = new ArrayList<>();
        
        String[] medicines = {"阿莫西林胶囊", "布洛芬片", "感冒灵颗粒", "维生素C片", "板蓝根颗粒"};
        Integer[] sales = {156, 142, 128, 115, 98};
        Double[] amounts = {2340.00, 2130.00, 1920.00, 1725.00, 1470.00};
        
        for (int i = 0; i < Math.min(medicines.length, limit != null ? limit : 5); i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("rank", i + 1);
            item.put("name", medicines[i]);
            item.put("sales", sales[i]);
            item.put("amount", amounts[i]);
            hotData.add(item);
        }
        
        return hotData;
    }

    @Override
    public List<Map<String, Object>> getLowStockData() {
        List<Map<String, Object>> lowStockData = new ArrayList<>();
        
        String[] medicines = {"阿莫西林胶囊", "布洛芬片", "感冒灵颗粒", "维生素C片"};
        Integer[] currentStock = {5, 12, 18, 8};
        Integer[] minStock = {20, 30, 25, 15};
        String[] statuses = {"严重不足", "库存不足", "库存不足", "库存不足"};
        
        for (int i = 0; i < medicines.length; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("name", medicines[i]);
            item.put("currentStock", currentStock[i]);
            item.put("minStock", minStock[i]);
            item.put("status", statuses[i]);
            lowStockData.add(item);
        }
        
        return lowStockData;
    }
    
    /**
     * 检查日期是否在指定范围内
     */
    private boolean isInDateRange(Date date, String startDate, String endDate) {
        if (date == null) return false;
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = startDate != null ? sdf.parse(startDate) : new Date(0);
            Date end = endDate != null ? sdf.parse(endDate) : new Date();
            
            return !date.before(start) && !date.after(end);
        } catch (Exception e) {
            return true; // 如果解析失败，默认包含
        }
    }
}
