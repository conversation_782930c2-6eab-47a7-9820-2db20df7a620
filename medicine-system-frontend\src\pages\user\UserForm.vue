<template>
  <div class="user-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      :disabled="mode === 'view'"
    >
      <el-form-item label="用户名" prop="username">
        <el-input 
          v-model="formData.username" 
          placeholder="请输入用户名"
          :disabled="mode === 'edit'"
        />
      </el-form-item>
      
      <el-form-item v-if="mode === 'add'" label="密码" prop="password">
        <el-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          show-password
        />
      </el-form-item>
      
      <el-form-item label="角色" prop="roleId">
        <el-select
          v-model="formData.roleId"
          placeholder="请选择角色"
          style="width: 100%"
        >
          <el-option
            v-for="item in roleOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">{{ mode === 'view' ? '返回' : '取消' }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { getUserDetail, addUser, updateUser } from '@/api/modules/user';
import { getAllRoles } from '@/api/modules/role';
import type { User, Role } from '@/types';

// 定义组件的props
const props = defineProps<{
  id?: number;
  mode?: 'add' | 'edit' | 'view';
}>();

// 定义组件的事件
const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'cancel'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<User>({
  username: '',
  password: '',
  roleId: undefined,
  status: 1  // 1表示正常，0表示停用
});

// 表单验证规则
const rules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  roleId: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
});

// 角色选项
const roleOptions = ref<{ label: string; value: number }[]>([]);

// 获取角色列表
const fetchRoles = async () => {
  try {
    const roleList = await getAllRoles();
    roleOptions.value = roleList.map(item => ({
      label: item.roleName,
      value: item.id!
    }));
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
};

// 获取用户详情
const fetchUserDetail = async (id: number) => {
  try {
    const detail = await getUserDetail(id);
    Object.keys(detail).forEach(key => {
      if (key in formData && key !== 'password') {
        formData[key as keyof User] = detail[key as keyof User];
      }
    });
  } catch (error: any) {
    ElMessage.error(error.msg || '获取用户详情失败');
  }
};

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (props.mode === 'edit' && props.id) {
          // 编辑时不传递密码字段
          const { password, ...updateData } = formData;
          await updateUser(props.id, updateData);
          ElMessage.success('更新成功');
        } else {
          await addUser(formData);
          ElMessage.success('添加成功');
        }
        emit('success');
      } catch (error: any) {
        ElMessage.error(error.msg || '操作失败');
      }
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
};

// 页面加载时获取数据
onMounted(async () => {
  await fetchRoles();
  
  if ((props.mode === 'edit' || props.mode === 'view') && props.id) {
    await fetchUserDetail(props.id);
  }
  
  // 如果是编辑模式，移除密码验证规则
  if (props.mode === 'edit') {
    delete rules.password;
  }
});
</script>

<style scoped>
.user-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>